import 'package:flutter_test/flutter_test.dart';
import 'package:online_medicine/APIs/cart_api_service.dart';

void main() {
  group('Cart API Service Tests', () {
    test('CartApiResponse should be created correctly', () {
      const response = CartApiResponse(
        success: true,
        message: 'Product added to cart successfully',
        statusCode: 200,
        data: {'product_id': 1, 'customer_id': 1},
      );

      expect(response.success, true);
      expect(response.message, 'Product added to cart successfully');
      expect(response.statusCode, 200);
      expect(response.isAlreadyInCart, false);
      expect(response.data, {'product_id': 1, 'customer_id': 1});
    });

    test('CartApiResponse should handle already in cart case', () {
      const response = CartApiResponse(
        success: false,
        message: 'Product is already in your cart',
        statusCode: 409,
        isAlreadyInCart: true,
      );

      expect(response.success, false);
      expect(response.message, 'Product is already in your cart');
      expect(response.statusCode, 409);
      expect(response.isAlreadyInCart, true);
    });

    test('CartApiResponse toString should work correctly', () {
      const response = CartApiResponse(
        success: true,
        message: 'Success',
        statusCode: 200,
      );

      final stringRepresentation = response.toString();
      expect(stringRepresentation, contains('success: true'));
      expect(stringRepresentation, contains('message: Success'));
      expect(stringRepresentation, contains('statusCode: 200'));
      expect(stringRepresentation, contains('isAlreadyInCart: false'));
    });
  });
}
