import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'api_config.dart';

/// Cart API Service
///
/// This service handles all cart-related API calls including
/// adding products to cart with proper error handling.
class CartApiService {
  /// Add product to cart
  ///
  /// Makes a POST request to add a product to the user's cart.
  /// 
  /// Parameters:
  /// - [productId]: The ID of the product to add to cart
  /// - [customerId]: The ID of the customer adding the product
  /// 
  /// Returns a [CartApiResponse] with success status and message.
  static Future<CartApiResponse> addToCart({
    required String productId,
    required String customerId,
  }) async {
    try {
      print('🛒 Adding product to cart - Product ID: $productId, Customer ID: $customerId');
      print('🔄 API URL: ${ApiConfig.addToCartUrl}');
      
      // Prepare request body
      final requestBody = {
        'product_id': int.tryParse(productId) ?? productId,
        'customer_id': int.tryParse(customerId) ?? customerId,
      };
      
      print('📤 Request body: ${json.encode(requestBody)}');
      
      // Make the HTTP POST request
      final response = await http
          .post(
            Uri.parse(ApiConfig.addToCartUrl),
            headers: ApiConfig.headers,
            body: json.encode(requestBody),
          )
          .timeout(ApiConfig.requestTimeout);
      
      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: ${response.body}');
      
      // Parse response
      final responseData = json.decode(response.body) as Map<String, dynamic>;
      
      // Handle different response status codes
      if (response.statusCode == 200 || response.statusCode == 201) {
        // Success - product added to cart
        return CartApiResponse(
          success: true,
          message: responseData['message'] as String? ?? 'Product added to cart successfully',
          statusCode: response.statusCode,
          data: responseData,
        );
      } else if (response.statusCode == 409) {
        // Product already in cart
        return CartApiResponse(
          success: false,
          message: responseData['message'] as String? ?? 'Product is already in your cart',
          statusCode: response.statusCode,
          data: responseData,
          isAlreadyInCart: true,
        );
      } else {
        // Other error responses
        return CartApiResponse(
          success: false,
          message: responseData['message'] as String? ?? 'Failed to add product to cart',
          statusCode: response.statusCode,
          data: responseData,
        );
      }
    } on SocketException {
      print('❌ Network error: No internet connection');
      return const CartApiResponse(
        success: false,
        message: 'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } on http.ClientException catch (e) {
      print('❌ HTTP Client error: $e');
      return CartApiResponse(
        success: false,
        message: 'Network error: ${e.message}',
        statusCode: 0,
      );
    } on FormatException catch (e) {
      print('❌ JSON parsing error: $e');
      return const CartApiResponse(
        success: false,
        message: 'Invalid response format from server',
        statusCode: 0,
      );
    } catch (e) {
      print('❌ Unexpected error: $e');
      return CartApiResponse(
        success: false,
        message: 'An unexpected error occurred: ${e.toString()}',
        statusCode: 0,
      );
    }
  }
}

/// Response model for cart API operations
class CartApiResponse {
  final bool success;
  final String message;
  final int statusCode;
  final Map<String, dynamic>? data;
  final bool isAlreadyInCart;

  const CartApiResponse({
    required this.success,
    required this.message,
    required this.statusCode,
    this.data,
    this.isAlreadyInCart = false,
  });

  @override
  String toString() {
    return 'CartApiResponse(success: $success, message: $message, statusCode: $statusCode, isAlreadyInCart: $isAlreadyInCart)';
  }
}
