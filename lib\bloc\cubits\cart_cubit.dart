import 'package:flutter_bloc/flutter_bloc.dart';
import '../states/cart_state.dart';
import '../states/auth_state.dart';
import '../../models/models.dart';
import '../../APIs/cart_api_service.dart';
import 'auth_cubit.dart';

/// Cubit for managing cart items and operations
class CartCubit extends Cubit<CartState> {
  final AuthCubit? _authCubit;

  CartCubit({AuthCubit? authCubit})
      : _authCubit = authCubit,
        super(const CartInitial());

  /// Load cart data
  Future<void> loadCart() async {
    emit(const CartLoading());

    try {
      // For now, start with empty cart since we only show real API data
      // In the future, this could load cart items from a "get cart" API endpoint
      emit(const CartLoaded(items: []));
    } catch (e) {
      emit(CartError(message: 'Failed to load cart: ${e.toString()}'));
    }
  }

  /// Add medicine to cart with API integration
  Future<void> addToCart(Medicine medicine, int quantity) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        // Get current user ID from auth state
        final currentUser = _authCubit?.state.user;
        if (currentUser == null) {
          emit(const CartError(message: 'Please login to add items to cart'));
          return;
        }

        // Call the add to cart API
        final response = await CartApiService.addToCart(
          productId: medicine.id,
          customerId: currentUser.id,
        );

        if (response.success) {
          // API call successful - add item to local cart state
          final updatedItems = List<CartItem>.from(currentState.items);
          final existingItemIndex =
              updatedItems.indexWhere((item) => item.id == medicine.id);

          if (existingItemIndex != -1) {
            // Update existing item quantity
            final existingItem = updatedItems[existingItemIndex];
            updatedItems[existingItemIndex] = existingItem.copyWith(
              cartQuantity: quantity,
            );
          } else {
            // Add new item to cart
            final newItem = CartItem.fromMedicine(medicine, quantity);
            updatedItems.add(newItem);
          }

          emit(CartOperationSuccess(
            message: response.message,
            operationType: existingItemIndex != -1
                ? CartOperationType.update
                : CartOperationType.add,
            items: updatedItems,
          ));

          // Return to loaded state
          emit(CartLoaded(items: updatedItems));
        } else {
          // API call failed
          if (response.isAlreadyInCart) {
            // Product already in cart - show specific message but don't treat as error
            emit(CartOperationSuccess(
              message: response.message,
              operationType: CartOperationType.add,
              items: currentState.items,
            ));
            emit(currentState.copyWith(isUpdating: false));
          } else {
            // Other API errors
            emit(CartError(message: response.message));
          }
        }
      } catch (e) {
        emit(CartError(message: 'Failed to add item to cart: ${e.toString()}'));
      }
    }
  }

  /// Update item quantity in cart
  void updateQuantity(String medicineId, int quantity) {
    final currentState = state;
    if (currentState is CartLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        final updatedItems = List<CartItem>.from(currentState.items);
        final itemIndex =
            updatedItems.indexWhere((item) => item.id == medicineId);

        if (itemIndex != -1) {
          if (quantity > 0) {
            // Update quantity
            updatedItems[itemIndex] = updatedItems[itemIndex].copyWith(
              cartQuantity: quantity,
            );
          } else {
            // Remove item if quantity is 0
            updatedItems.removeAt(itemIndex);
          }

          emit(CartLoaded(items: updatedItems));
        }
      } catch (e) {
        emit(CartError(
            message: 'Failed to update item quantity: ${e.toString()}'));
      }
    }
  }

  /// Remove item from cart
  void removeFromCart(String medicineId) {
    final currentState = state;
    if (currentState is CartLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        final updatedItems = List<CartItem>.from(currentState.items);
        final itemIndex =
            updatedItems.indexWhere((item) => item.id == medicineId);

        if (itemIndex != -1) {
          final removedItem = updatedItems[itemIndex];
          updatedItems.removeAt(itemIndex);

          emit(CartOperationSuccess(
            message: '${removedItem.name} removed from cart',
            operationType: CartOperationType.remove,
            items: updatedItems,
          ));

          // Return to loaded state
          emit(CartLoaded(items: updatedItems));
        }
      } catch (e) {
        emit(CartError(
            message: 'Failed to remove item from cart: ${e.toString()}'));
      }
    }
  }

  /// Clear all items from cart
  void clearCart() {
    final currentState = state;
    if (currentState is CartLoaded) {
      emit(currentState.copyWith(isUpdating: true));

      try {
        emit(const CartOperationSuccess(
          message: 'Cart cleared',
          operationType: CartOperationType.clear,
          items: [],
        ));

        // Return to loaded state with empty cart
        emit(const CartLoaded(items: []));
      } catch (e) {
        emit(CartError(message: 'Failed to clear cart: ${e.toString()}'));
      }
    }
  }

  /// Get quantity of a specific medicine in cart
  int getQuantity(String medicineId) {
    final currentState = state;
    if (currentState is CartLoaded) {
      return currentState.getQuantity(medicineId);
    }
    return 0;
  }

  /// Check if a medicine is in cart
  bool containsMedicine(String medicineId) {
    final currentState = state;
    if (currentState is CartLoaded) {
      return currentState.containsMedicine(medicineId);
    }
    return false;
  }
}
